'use client';

import { BsSliders } from 'react-icons/bs';
import { useEffect, useState, useRef, useCallback, useLayoutEffect } from 'react';

import RecommendationCard from './RecommendationCard';

const Recommendation = () => {
  const [visibleCards, setVisibleCards] = useState(3);
  const [currentStartIndex, setCurrentStartIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  const containerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const intersectionObserverRef = useRef<IntersectionObserver | null>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  const mutationObserverRef = useRef<MutationObserver | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const recommendations = [
    {
      title: 'Town Browsing',
      duration: '4 nights / 5 days',
      location: 'Aspen',
      tags: 'Activities, Explore, Leisure, Family',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'badge',
    },
    {
      title: 'Mountain Retreat',
      duration: '3 nights / 4 days',
      location: 'Colorado',
      tags: 'Nature, Relax, Hiking',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'Top Rated',
    },
    {
      title: 'City Lights',
      duration: '2 nights / 3 days',
      location: 'New York',
      tags: 'Urban, Nightlife, Shopping',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'Hot',
    },
    {
      title: 'Beach Paradise',
      duration: '5 nights / 6 days',
      location: 'Hawaii',
      tags: 'Beach, Relaxation, Water Sports',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'Popular',
    },
    {
      title: 'Cultural Journey',
      duration: '6 nights / 7 days',
      location: 'Japan',
      tags: 'Culture, History, Food',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'New',
    },
  ];

  const calculateVisibleCards = useCallback((retryCount = 0) => {
    if (containerRef.current && headerRef.current) {
      // Force a reflow to ensure accurate measurements
      containerRef.current.offsetHeight;
      headerRef.current.offsetHeight;

      const containerHeight = containerRef.current.clientHeight;
      const headerHeight = headerRef.current.clientHeight;
      const viewAllButtonHeight = 24; // Actual height of View All button (h-[24px])
      const padding = 16; // More accurate padding (py-2 = 8px top + 8px bottom)
      const marginTop = 4; // mt-1 = 4px
      const cardGap = 2; // gap-0.5 between cards (2px)

      // Check if dimensions are valid (not zero)
      if (containerHeight === 0 || headerHeight === 0) {
        // Retry calculation after a short delay if dimensions are not ready
        if (retryCount < 10) { // Increased retry count
          retryTimeoutRef.current = setTimeout(() => {
            calculateVisibleCards(retryCount + 1);
          }, 100 * (retryCount + 1)); // Longer delays
        }
        return;
      }

      // More accurate height calculation
      const availableHeight = containerHeight - headerHeight - viewAllButtonHeight - padding - marginTop;

      // Get actual card height from DOM if possible
      const cardElements = containerRef.current.querySelectorAll('[data-card="recommendation"]');
      let cardHeight = 110; // Reduced default fallback to be more optimistic

      if (cardElements.length > 0) {
        const firstCard = cardElements[0] as HTMLElement;
        // Use actual height without adding extra gap since gap is handled separately
        cardHeight = firstCard.offsetHeight;
      }

      // Calculate total height needed for cards including gaps
      const calculateTotalCardHeight = (numCards: number) => {
        return (numCards * cardHeight) + ((numCards - 1) * cardGap);
      };

      // Find the maximum number of cards that can fit
      let maxCards = 1;
      for (let i = 1; i <= recommendations.length; i++) {
        const totalHeightNeeded = calculateTotalCardHeight(i);
        if (totalHeightNeeded <= availableHeight) {
          maxCards = i;
        } else {
          break;
        }
      }

      const newVisibleCards = Math.max(1, Math.min(maxCards, recommendations.length));

      // Temporary debugging - remove after testing
      if (retryCount === 0) {
        console.log('Height calculation debug:', {
          containerHeight,
          headerHeight,
          viewAllButtonHeight,
          padding,
          marginTop,
          availableHeight,
          cardHeight,
          maxCards,
          newVisibleCards,
          totalHeightForMaxCards: calculateTotalCardHeight(maxCards),
          totalHeightForMaxPlusOne: calculateTotalCardHeight(maxCards + 1)
        });
      }

      setVisibleCards(prev => {
        if (prev !== newVisibleCards) {
          setCurrentStartIndex(0); // Reset to start when card count changes
          return newVisibleCards;
        }
        return prev;
      });
    } else if (retryCount < 10) {
      // Retry if refs are not ready
      retryTimeoutRef.current = setTimeout(() => {
        calculateVisibleCards(retryCount + 1);
      }, 100 * (retryCount + 1));
    }
  }, [recommendations.length]);

  // Use layoutEffect for immediate DOM measurements
  useLayoutEffect(() => {
    // Delay calculation to ensure CSS is applied
    const timer = setTimeout(() => {
      calculateVisibleCards();
    }, 0);

    return () => clearTimeout(timer);
  }, [calculateVisibleCards]);

  useEffect(() => {
    // Debounced resize handler
    const handleResize = () => {
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }

      resizeTimeoutRef.current = setTimeout(() => {
        calculateVisibleCards();
      }, 150); // Slightly longer debounce
    };

    // Set up ResizeObserver for more accurate size detection
    if (containerRef.current && typeof ResizeObserver !== 'undefined') {
      resizeObserverRef.current = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (entry.target === containerRef.current) {
            // Container size changed, recalculate
            setTimeout(() => calculateVisibleCards(), 50);
            break;
          }
        }
      });

      resizeObserverRef.current.observe(containerRef.current);
    }

    // Set up intersection observer to detect when component becomes visible
    if (containerRef.current) {
      intersectionObserverRef.current = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              // Component is visible, recalculate dimensions with delay
              setTimeout(() => calculateVisibleCards(), 100);
            }
          });
        },
        { threshold: 0.1 }
      );

      intersectionObserverRef.current.observe(containerRef.current);
    }

    // Set up mutation observer to detect DOM changes
    if (containerRef.current) {
      mutationObserverRef.current = new MutationObserver((mutations) => {
        let shouldRecalculate = false;
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' || mutation.type === 'attributes') {
            shouldRecalculate = true;
          }
        });

        if (shouldRecalculate) {
          setTimeout(() => calculateVisibleCards(), 100);
        }
      });

      mutationObserverRef.current.observe(containerRef.current, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style']
      });
    }

    // Handle visibility changes (tab switching, etc.)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible, recalculate dimensions
        setTimeout(() => calculateVisibleCards(), 100);
      }
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
      document.removeEventListener('visibilitychange', handleVisibilityChange);

      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }

      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }

      if (intersectionObserverRef.current) {
        intersectionObserverRef.current.disconnect();
      }

      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }

      if (mutationObserverRef.current) {
        mutationObserverRef.current.disconnect();
      }
    };
  }, [calculateVisibleCards]);

  // Auto-next functionality with hover pause
  useEffect(() => {
    if (recommendations.length <= visibleCards || isHovered) return;

    const interval = setInterval(() => {
      setCurrentStartIndex(prev => {
        const maxStartIndex = recommendations.length - visibleCards;
        return prev >= maxStartIndex ? 0 : prev + 1;
      });
    }, 4000); // Change cards every 4 seconds (slower for better UX)

    return () => clearInterval(interval);
  }, [visibleCards, recommendations.length, isHovered]);

  const displayedRecommendations = recommendations.slice(
    currentStartIndex,
    currentStartIndex + visibleCards
  );

  // Recalculate when displayed recommendations change
  useEffect(() => {
    const timer = setTimeout(() => {
      calculateVisibleCards();
    }, 200); // Give time for DOM to update

    return () => clearTimeout(timer);
  }, [displayedRecommendations.length, calculateVisibleCards]);

  return (
    <div
      ref={containerRef}
      className="bg-white h-full px-4 py-2 rounded-xl flex flex-col overflow-hidden"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div ref={headerRef} className="flex flex-row items-center justify-between flex-shrink-0">
        <div>
          <p className="text-[#1C1C1C] text-lg font-bold">Recommendation</p>
        </div>
        <div className="flex flex-row items-center gap-2">
          <BsSliders />
          <p className="text-sm font-medium">Filter</p>
        </div>
      </div>
      <div className="flex-1 mt-1 flex flex-col justify-between min-h-0 overflow-hidden">
        <RecommendationCard
          recommendations={displayedRecommendations}
          showViewAll={true}
        />
      </div>
    </div>
  );
};

export default Recommendation;
