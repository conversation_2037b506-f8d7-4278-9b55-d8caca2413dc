import { SlLocationPin } from 'react-icons/sl';

const PrimaryFilter = () => {
  return (
    <div className="grid grid-cols-5 grid-flow-row w-full bg-white rounded-xl p-4">
      <div>
        <p className="text-[#1C1C1C] text-base font-medium opacity-50 uppercase">
          LOCATION
        </p>
        <div className="flex items-center space-x-2 mt-1.5">
          <SlLocationPin size={20} />
          <p className="text-[#1C1C1C] text-sm font-medium">Where to go</p>
        </div>
      </div>
      <div className=" border-l-2 pl-4">
        {' '}
        <p className="text-[#1C1C1C] text-base font-medium opacity-50 uppercase">
          LOCATION
        </p>
        <div className="flex items-center space-x-2 mt-1.5">
          <SlLocationPin size={20} />
          <p className="text-[#1C1C1C] text-sm font-medium">Where to go</p>
        </div>
      </div>

      <div className=" border-l-2 pl-4">
        {' '}
        <p className="text-[#1C1C1C] text-base font-medium opacity-50 uppercase">
          LOCATION
        </p>
        <div className="flex items-center space-x-2 mt-1.5">
          <SlLocationPin size={20} />
          <p className="text-[#1C1C1C] text-sm font-medium">Where to go</p>
        </div>
      </div>

      <div className=" border-l-2 pl-4">
        {' '}
        <p className=" uppercase text-[#1C1C1C] text-base font-medium opacity-50">
          LOCATION
        </p>
        <div className="flex items-center space-x-2 mt-1.5">
          <SlLocationPin size={20} />
          <p className="text-[#1C1C1C] text-sm font-medium">Where to go</p>
        </div>
      </div>

      <div className=" border-l-2 pl-4">
        <p className="text-[#1C1C1C] text-base font-medium opacity-50 uppercase">
          LOCATION
        </p>
        <div className="flex items-center space-x-2 mt-1.5">
          <SlLocationPin size={20} />
          <p className="text-[#1C1C1C] text-sm font-medium">Where to go</p>
        </div>
      </div>
    </div>
  );
};

export default PrimaryFilter;
