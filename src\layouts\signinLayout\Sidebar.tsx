'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import * as Icons from 'react-icons/fi';
import { FiChevronDown } from 'react-icons/fi';

import './Sidebar.css';
import { MENU_SECTIONS, GUEST_MENU_SECTIONS } from './menuData';

type SidebarProps = {
  isOpenFromHeader?: boolean;
  setIsOpenFromHeader?: any;
};

const isLoggedIn = true;

export default function Sidebar({
  isOpenFromHeader = false,
  setIsOpenFromHeader = () => {},
}: SidebarProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth <= 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (isMobile) {
      setIsExpanded(isOpenFromHeader);
    }
  }, [isOpenFromHeader, isMobile]);

  const toggleMobile = () => {
    if (isMobile) {
      setIsExpanded(prev => {
        const newVal = !prev;
        setIsOpenFromHeader(newVal);
        return newVal;
      });
    }
  };

  const menu = isLoggedIn ? MENU_SECTIONS : GUEST_MENU_SECTIONS;

  return (
    <div
      className={`sidebar-wrapper relative w-full ${
        isExpanded ? 'expanded' : ''
      } ${isMobile ? 'mobile' : ''} ${
        !isExpanded && isMobile ? 'collapsed' : ''
      }`}
      onMouseEnter={() => !isMobile && setIsExpanded(true)}
      onMouseLeave={() => !isMobile && setIsExpanded(false)}
    >
      {isMobile && isExpanded && (
        <button
          type="button"
          className="absolute top-1 right-3 z-50 text-xl text-gray-700"
          onClick={toggleMobile}
        >
          ✕
        </button>
      )}

      {/* Logo */}
      <div className="flex justify-between items-center p-4">
        <div className="flex items-center gap-2">
          <Image
            src="https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/logo.svg"
            alt="User"
            width={40}
            height={40}
          />
          <div
            className={`sidebar-profile-info ${isExpanded ? 'visible' : 'hidden'}`}
          >
            <span className="logo-text">nxVoy</span>
          </div>
        </div>
      </div>

      {/* Menu */}
      <div className="flex flex-col flex-1 gap-3 p-3">
        {menu.map(({ section, items }) => (
          <div key={section}>
            <div
              className={`sidebar-section ${isExpanded ? 'visible' : 'invisible'}`}
            >
              {section}
            </div>
            {items.map(({ icon, label, path, highlight }) => {
              const IconComponent = Icons[icon as keyof typeof Icons];
              return (
                <Link
                  href={path}
                  key={label}
                  className={`sidebar-link flex items-center hover:bg-[#F5F5F5] ${
                    isExpanded ? 'justify-start' : 'justify-center'
                  } ${highlight ? 'recommend' : ''}`}
                >
                  <div className="icon-wrapper">
                    <IconComponent />
                  </div>
                  <span
                    className={`label-text text-subtitle ${isExpanded ? 'visible' : 'hidden'}`}
                  >
                    {label}
                  </span>
                </Link>
              );
            })}
          </div>
        ))}
      </div>

      {/* Profile */}
      {isLoggedIn && (
        <div className="flex justify-between items-center p-4">
          <div className="flex items-center gap-2">
            <Image
              src="https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/logo.svg"
              alt="User"
              width={40}
              height={40}
            />
            <div
              className={`sidebar-profile-info ${isExpanded ? 'visible' : 'hidden'}`}
            >
              <span className="sidebar-name">Danie Jay</span>
              <span className="sidebar-username">@Danie.Jay82</span>
            </div>
          </div>
          {isExpanded && <FiChevronDown size={16} />}
        </div>
      )}
    </div>
  );
}
