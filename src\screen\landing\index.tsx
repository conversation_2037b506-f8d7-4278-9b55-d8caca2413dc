import PrimaryFilter from '@/components/globalComponents/primaryFilter';

import Recommendation from './Recommendation';

const LandingPage = () => {
  return (
    <div className="p-5 rounded-tl-xl h-[calc(100vh-73px)] flex flex-col">
      <div>
        <p className="text-base text-[#1C1C1C] opacity-75">Welcome to NxVoy!</p>
        <div className=" text-4xl font-semibold">
          <span className="text-gradient">Meet Shasa</span>, Your Travel
          Companion!
        </div>
      </div>
      <div className="py-4">
        <PrimaryFilter />
      </div>
      <div className="grid grid-cols-5 gap-4 flex-1 min-h-0">
        <div className="col-span-3 bg-[#17074C] rounded-xl text-center text-xl text-white flex items-center justify-center">
          chat
        </div>
        <div className="col-span-2">
          <Recommendation />
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
